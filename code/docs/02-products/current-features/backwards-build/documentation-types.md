# Types of Documentations to Generate
REQUIREMENTS (Business Needs)
├── User Stories (Who needs what and why)
├── Business Requirements (Objectives)
├── Functional Requirements (What it must do)
└── Non-functional Requirements (How well it must do it)
    ↓
SPECIFICATIONS (Technical Intent)
├── Design Specs (How to achieve requirements)
├── API Specs (Interfaces and contracts)
├── Behavior Specs (Detailed system behavior)
└── Test Specs (Verification criteria)
    ↓
IMPLEMENTATION
├── Code
├── Configuration
└── Infrastructure
    ↓
DOCUMENTATION (Usage)
├── API Docs
├── User Guides
└── Tutorials
