# Progress Tracking System Specification

_Feature ID: MVP-PROGRESS-001_  
_Last updated: July 15, 2025_

## Overview

A visual progress tracking system that gamifies the improvement journey, showing users their project's readiness percentage increase in real-time. This creates momentum and motivation to complete the improvement process.

## Core Concept

Transform abstract code quality into a tangible, improving number that users can feel proud of as it increases from initial assessment (e.g., 23%) to deployment-ready (e.g., 82%).

## Visual Design

### Main Progress Display

```
┌─────────────────────────────────────────┐
│  Project Readiness Progress             │
├─────────────────────────────────────────┤
│                                         │
│  Current: 47% (+24% today!)             │
│  ▓▓▓▓▓▓▓▓▓░░░░░░░░░░░░░░░░░           │
│                                         │
│  🔴 → 🟡 → 🟢                          │
│  23%   47%   Goal: 80%                 │
│                                         │
│  ⏱️ Time invested: 34 minutes            │
│  🔧 Issues fixed: 7 of 15               │
│                                         │
└─────────────────────────────────────────┘
```

### Milestone Celebrations

```
┌─────────────────────────────────────────┐
│  🎉 Milestone Reached!                  │
├─────────────────────────────────────────┤
│                                         │
│  You just hit 50% readiness!           │
│                                         │
│  🏆 "Halfway Hero" Achievement Unlocked │
│                                         │
│  Your app is now:                      │
│  • More secure than 60% of projects    │
│  • Won't crash on basic operations     │
│  • Has proper error handling           │
│                                         │
│  [Continue Improving] [Share Progress]  │
│                                         │
└─────────────────────────────────────────┘
```

## Scoring Algorithm

### Category Weights

```typescript
interface ScoringWeights {
  security: 0.30;      // 30% - Most critical
  errorHandling: 0.25; // 25% - Prevents crashes
  performance: 0.20;   // 20% - User experience
  codeQuality: 0.15;   // 15% - Maintainability
  documentation: 0.10; // 10% - Team enablement
}
```

### Calculation Method

```typescript
class ReadinessCalculator {
  calculateScore(analysis: ProjectAnalysis): ReadinessScore {
    const scores = {
      security: this.scoreSecurityIssues(analysis.security),
      errorHandling: this.scoreErrorHandling(analysis.errors),
      performance: this.scorePerformance(analysis.performance),
      codeQuality: this.scoreCodeQuality(analysis.quality),
      documentation: this.scoreDocumentation(analysis.docs),
    };
    
    // Weighted average
    const totalScore = Object.entries(scores).reduce(
      (total, [category, score]) => 
        total + score * WEIGHTS[category],
      0
    );
    
    return {
      percentage: Math.round(totalScore * 100),
      breakdown: scores,
      grade: this.getGrade(totalScore),
      improvements: this.getPossibleImprovements(scores),
    };
  }
  
  private scoreSecurityIssues(issues: SecurityIssue[]): number {
    const criticalCount = issues.filter(i => i.severity === 'critical').length;
    const highCount = issues.filter(i => i.severity === 'high').length;
    
    if (criticalCount > 0) return 0; // F grade
    if (highCount > 2) return 0.2;   // D grade
    if (highCount > 0) return 0.5;   // C grade
    // ... continue scoring logic
    
    return 1.0; // A grade
  }
}
```

### Issue Impact Values

| Issue Type | Points per Fix | Example |
|------------|----------------|---------|
| **Critical Security** | +8-12% | Exposed API keys |
| **High Security** | +4-6% | SQL injection risk |
| **Error Handling** | +3-5% | Unhandled promises |
| **Performance** | +2-4% | Bundle optimization |
| **Code Quality** | +1-3% | Complexity reduction |
| **Documentation** | +1-2% | Adding JSDoc |

## Progress Persistence

### Storage Structure

```typescript
interface ProgressData {
  projectId: string;
  history: ProgressSnapshot[];
  currentScore: number;
  startScore: number;
  peakScore: number;
  totalTime: number;
  issuesFixed: string[];
  achievements: Achievement[];
}

interface ProgressSnapshot {
  timestamp: Date;
  score: number;
  action: string;
  category: string;
  timeTaken: number;
}
```

### File Storage

```
.kapi/
└── progress/
    ├── current.json     # Current session progress
    ├── history.json     # All-time progress history
    └── achievements.json # Unlocked achievements
```

## Real-Time Updates

### Progress Animation

```typescript
class ProgressAnimator {
  animateScoreChange(
    from: number, 
    to: number, 
    duration: number = 1500
  ): void {
    const element = document.getElementById('progress-bar');
    const scoreElement = document.getElementById('score-display');
    
    // Smooth animation with easing
    const startTime = Date.now();
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Easing function for smooth animation
      const eased = this.easeOutCubic(progress);
      const currentScore = from + (to - from) * eased;
      
      // Update visual elements
      element.style.width = `${currentScore}%`;
      scoreElement.textContent = `${Math.round(currentScore)}%`;
      
      // Particle effects at milestones
      if (Math.floor(currentScore) % 10 === 0) {
        this.showParticles();
      }
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        this.checkMilestone(to);
      }
    };
    
    animate();
  }
}
```

### Live Progress Events

```typescript
interface ProgressEvent {
  type: 'fix_applied' | 'test_passed' | 'doc_generated';
  category: string;
  pointsEarned: number;
  newScore: number;
  message: string;
}

class ProgressTracker {
  private eventEmitter = new EventEmitter();
  
  trackFix(fix: Fix): void {
    const points = this.calculatePoints(fix);
    const newScore = this.currentScore + points;
    
    this.eventEmitter.emit('progress', {
      type: 'fix_applied',
      category: fix.category,
      pointsEarned: points,
      newScore,
      message: `Fixed ${fix.description} (+${points}%)`,
    });
    
    // Update storage
    this.saveProgress(newScore, fix);
    
    // Trigger animation
    this.animator.animateScoreChange(this.currentScore, newScore);
    
    this.currentScore = newScore;
  }
}
```

## Achievement System

### Achievement Types

```typescript
interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  condition: (progress: ProgressData) => boolean;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

const achievements: Achievement[] = [
  {
    id: 'first_fix',
    name: 'First Step',
    description: 'Fix your first issue',
    icon: '🎯',
    condition: (p) => p.issuesFixed.length >= 1,
    rarity: 'common',
  },
  {
    id: 'halfway_hero',
    name: 'Halfway Hero',
    description: 'Reach 50% readiness',
    icon: '🏆',
    condition: (p) => p.currentScore >= 50,
    rarity: 'rare',
  },
  {
    id: 'security_master',
    name: 'Security Master',
    description: 'Fix all security issues',
    icon: '🛡️',
    condition: (p) => p.breakdown.security === 100,
    rarity: 'epic',
  },
  {
    id: 'speed_demon',
    name: 'Speed Demon',
    description: 'Go from <30% to >80% in under 1 hour',
    icon: '⚡',
    condition: (p) => p.totalTime < 3600 && p.currentScore > 80,
    rarity: 'legendary',
  },
];
```

### Achievement Notification

```
┌─────────────────────────────────────────┐
│  🏆 Achievement Unlocked!               │
├─────────────────────────────────────────┤
│                                         │
│  ⚡ SPEED DEMON (Legendary)             │
│                                         │
│  You improved your project from 23% to  │
│  82% in just 47 minutes!                │
│                                         │
│  Rarity: Only 2% of developers achieve  │
│  this speed!                            │
│                                         │
│  [Share Achievement] [View All]         │
│                                         │
└─────────────────────────────────────────┘
```

## Progress Timeline View

```
┌─────────────────────────────────────────┐
│  Your Improvement Journey               │
├─────────────────────────────────────────┤
│                                         │
│  📍 9:00 AM - Started (23%)             │
│  │                                      │
│  ├─ 9:05 AM - Fixed API keys (+8%)     │
│  │  └─ "Much more secure now!"          │
│  │                                      │
│  ├─ 9:12 AM - Added error handling (+5%)│
│  │  └─ "No more crashes!"               │
│  │                                      │
│  ├─ 9:18 AM - Milestone: 50% 🎉         │
│  │                                      │
│  ├─ 9:25 AM - Optimized bundle (+4%)   │
│  │  └─ "60% smaller!"                   │
│  │                                      │
│  └─ 9:47 AM - Current (82%) 🚀          │
│                                         │
│  [Export Timeline] [Continue]           │
│                                         │
└─────────────────────────────────────────┘
```

## Integration Points

### With Text-Based Interview

```typescript
// After interview completion
const userGoals = interview.getGoals();
const targetScore = this.calculateTargetScore(userGoals);

// Show personalized target
ui.showProgress({
  current: analysis.score,
  target: targetScore,
  message: `Based on your goal "${userGoals.primary}", 
            we recommend reaching ${targetScore}% readiness.`,
});
```

### With Fix Application

```typescript
// When applying fixes
fixManager.on('fixApplied', (fix) => {
  progressTracker.trackFix(fix);
  
  // Update UI immediately
  ui.updateProgress({
    animation: true,
    showParticles: fix.impact > 5,
    celebration: progressTracker.checkMilestone(),
  });
});
```

### With Deployment

```typescript
// Final celebration before deployment
if (progress.currentScore >= 80) {
  ui.showDeploymentReady({
    score: progress.currentScore,
    journey: progress.getJourneySummary(),
    achievements: progress.achievements,
    nextStep: 'deployment',
  });
}
```

## Success Metrics

- **Engagement**: Average session includes 5+ fixes
- **Completion**: 60% reach 80%+ readiness
- **Time**: Average 45 minutes to improve 40%+
- **Satisfaction**: 4.7/5 rating for motivation

## Future Enhancements

1. **Team Leaderboards**: Compare progress with others
2. **Progress Predictions**: ML-based time estimates
3. **Custom Goals**: User-defined target scores
4. **Progress Sharing**: Social media integration
5. **Historical Comparison**: Track improvement over projects

## Related Features

- [Brutal Honesty Assessment](../brutal-honesty/ai-project-analysis.md)
- [Achievement System](./03-achievement-system.md)
- [Fix Automation](./05-auto-fix-system.md)