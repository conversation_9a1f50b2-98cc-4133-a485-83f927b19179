# Text-Based Interview System Specification

_Feature ID: MVP-INTERVIEW-001_  
_Last updated: July 15, 2025_

## Overview

A conversational text-based interview system that understands developer goals and personalizes the improvement journey. This creates a collaborative atmosphere and ensures fixes align with immediate needs.

## Core Concept

After showing the brutal honesty report and reverse-engineered documentation, KAPI engages in a friendly text conversation to understand what the developer actually wants to achieve, making the improvement path personal and relevant.

## User Experience

### Interview Initiation

```
┌─────────────────────────────────────────┐
│  💬 Let's Align on Your Goals           │
├─────────────────────────────────────────┤
│                                         │
│  KAPI: I've analyzed your project and   │
│  created a blueprint. Before we start   │
│  fixing things, I'd like to understand  │
│  what's most important to you.          │
│                                         │
│  What's your main goal with this        │
│  project right now?                     │
│                                         │
│  [Type your response...]                │
│  _                                      │
│                                         │
│  Common goals:                          │
│  • "Need it working for demo tomorrow"  │
│  • "Want to deploy it live"             │
│  • "Make it more maintainable"          │
│  • "Fix the crashes"                    │
│                                         │
└─────────────────────────────────────────┘
```

### Conversational Flow

```
┌─────────────────────────────────────────┐
│  💬 Understanding Your Priorities        │
├─────────────────────────────────────────┤
│                                         │
│  You: "I have a demo tomorrow at 2pm    │
│  and the app keeps crashing when users  │
│  try to login"                          │
│                                         │
│  KAPI: Got it! A demo tomorrow - that's │
│  tight timing. I see the login crashes  │
│  are caused by unhandled promises in    │
│  your auth flow.                        │
│                                         │
│  Would you rather:                      │
│  1) Quick fix just the login (20 min)   │
│  2) Fix all critical errors (45 min)    │
│  3) Make it demo-ready (1.5 hours)      │
│                                         │
│  [Type 1, 2, or 3, or explain more...]  │
│  _                                      │
│                                         │
└─────────────────────────────────────────┘
```

### Dynamic Prioritization

```
┌─────────────────────────────────────────┐
│  💬 Adjusting the Plan                  │
├─────────────────────────────────────────┤
│                                         │
│  You: "Actually, I also need user       │
│  registration working. My boss wants     │
│  to create an account during the demo"  │
│                                         │
│  KAPI: Understood! Let me adjust the    │
│  plan. With login AND registration,     │
│  here's what I recommend:               │
│                                         │
│  Priority fixes for your demo:          │
│  1. Fix login crashes (15 min)          │
│  2. Fix registration flow (20 min)      │
│  3. Add basic error messages (10 min)   │
│  4. Quick UI polish (15 min)            │
│                                         │
│  Total time: ~1 hour                    │
│  Readiness: 23% → 68% (demo-ready!)     │
│                                         │
│  Sound good? We can tackle security     │
│  and performance after your demo.       │
│                                         │
│  [Let's start] [Adjust plan] [Details]  │
│                                         │
└─────────────────────────────────────────┘
```

## Technical Implementation

### Interview Engine

```typescript
interface InterviewContext {
  projectAnalysis: ProjectAnalysis;
  userResponses: UserResponse[];
  detectedIntent: Intent;
  timeline: TimeConstraint;
  priorities: Priority[];
}

class InterviewEngine {
  private nlp: NLPProcessor;
  private contextBuilder: ContextBuilder;
  
  async startInterview(analysis: ProjectAnalysis): Promise<void> {
    const opening = this.generateOpening(analysis);
    await this.display(opening);
    
    const context: InterviewContext = {
      projectAnalysis: analysis,
      userResponses: [],
      detectedIntent: null,
      timeline: null,
      priorities: []
    };
    
    await this.conductInterview(context);
  }
  
  private async conductInterview(context: InterviewContext): Promise<Plan> {
    let complete = false;
    
    while (!complete) {
      const response = await this.getUserInput();
      context.userResponses.push(response);
      
      // Process response
      const intent = await this.nlp.extractIntent(response);
      const entities = await this.nlp.extractEntities(response);
      
      // Update context
      this.updateContext(context, intent, entities);
      
      // Generate next question or recommendation
      const next = this.generateNextInteraction(context);
      
      if (next.type === 'recommendation') {
        const confirmed = await this.presentRecommendation(next);
        if (confirmed) {
          complete = true;
          return this.generatePlan(context);
        }
      } else {
        await this.display(next.message);
      }
    }
  }
}
```

### Natural Language Processing

```typescript
class NLPProcessor {
  async extractIntent(text: string): Promise<Intent> {
    // Keyword and pattern matching
    const patterns = {
      demo: /demo|presentation|show|meeting/i,
      deploy: /deploy|live|production|ship/i,
      fix: /fix|crash|error|broken/i,
      maintain: /maintain|clean|refactor|readable/i,
      urgent: /tomorrow|today|asap|urgent|now/i
    };
    
    const intents: Intent[] = [];
    
    for (const [intent, pattern] of Object.entries(patterns)) {
      if (pattern.test(text)) {
        intents.push({
          type: intent,
          confidence: this.calculateConfidence(text, pattern)
        });
      }
    }
    
    return this.selectBestIntent(intents);
  }
  
  async extractEntities(text: string): Promise<Entity[]> {
    const entities: Entity[] = [];
    
    // Time entities
    const timeMatch = text.match(/(\d+)\s*(hour|minute|day|tomorrow|today)/i);
    if (timeMatch) {
      entities.push({
        type: 'time',
        value: this.parseTime(timeMatch[0]),
        raw: timeMatch[0]
      });
    }
    
    // Feature entities
    const features = ['login', 'registration', 'authentication', 'payment', 'api'];
    for (const feature of features) {
      if (text.toLowerCase().includes(feature)) {
        entities.push({
          type: 'feature',
          value: feature,
          context: this.extractContext(text, feature)
        });
      }
    }
    
    // Problem entities
    const problems = ['crash', 'error', 'slow', 'broken', 'security'];
    for (const problem of problems) {
      if (text.toLowerCase().includes(problem)) {
        entities.push({
          type: 'problem',
          value: problem,
          severity: this.assessSeverity(text, problem)
        });
      }
    }
    
    return entities;
  }
}
```

### Dynamic Response Generation

```typescript
class ResponseGenerator {
  generateResponse(
    context: InterviewContext,
    intent: Intent,
    entities: Entity[]
  ): InterviewResponse {
    // Acknowledge understanding
    const acknowledgment = this.acknowledgeInput(intent, entities);
    
    // Provide insight
    const insight = this.provideInsight(context, entities);
    
    // Ask clarifying question or make recommendation
    const next = this.determineNextStep(context);
    
    return {
      message: `${acknowledgment} ${insight}\n\n${next.content}`,
      type: next.type,
      options: next.options
    };
  }
  
  private acknowledgeInput(intent: Intent, entities: Entity[]): string {
    const templates = {
      demo: "Got it! A demo {time} - that's {urgency}.",
      deploy: "You want to get this live. I can help with that!",
      fix: "I see, the {problem} issues are blocking you.",
      urgent: "I understand this is urgent. Let's focus on the essentials."
    };
    
    return this.fillTemplate(templates[intent.type], entities);
  }
  
  private provideInsight(
    context: InterviewContext,
    entities: Entity[]
  ): string {
    const insights = [];
    
    for (const entity of entities) {
      if (entity.type === 'feature') {
        const issues = this.findRelatedIssues(
          context.projectAnalysis,
          entity.value
        );
        
        if (issues.length > 0) {
          insights.push(
            `I see the ${entity.value} has ${issues.length} issues, ` +
            `mainly ${issues[0].type}.`
          );
        }
      }
    }
    
    return insights.join(' ');
  }
}
```

### Plan Customization

```typescript
class PlanCustomizer {
  customizePlan(
    baseAnalysis: ProjectAnalysis,
    context: InterviewContext
  ): CustomizedPlan {
    const plan = {
      fixes: [],
      estimatedTime: 0,
      targetReadiness: 0,
      phases: []
    };
    
    // Prioritize based on user goals
    if (context.detectedIntent.type === 'demo') {
      // Focus on visible issues and stability
      plan.fixes = this.prioritizeForDemo(baseAnalysis.issues);
      plan.phases = [
        {
          name: 'Critical Stability',
          fixes: this.filterCritical(plan.fixes),
          time: '20 min'
        },
        {
          name: 'User Experience',
          fixes: this.filterUX(plan.fixes),
          time: '15 min'
        },
        {
          name: 'Polish',
          fixes: this.filterPolish(plan.fixes),
          time: '10 min'
        }
      ];
    } else if (context.detectedIntent.type === 'deploy') {
      // Focus on security and reliability
      plan.fixes = this.prioritizeForProduction(baseAnalysis.issues);
      plan.phases = [
        {
          name: 'Security Hardening',
          fixes: this.filterSecurity(plan.fixes),
          time: '30 min'
        },
        {
          name: 'Error Handling',
          fixes: this.filterErrorHandling(plan.fixes),
          time: '25 min'
        },
        {
          name: 'Performance',
          fixes: this.filterPerformance(plan.fixes),
          time: '20 min'
        }
      ];
    }
    
    // Calculate realistic readiness target
    plan.targetReadiness = this.calculateAchievableReadiness(
      baseAnalysis.currentReadiness,
      plan.fixes,
      context.timeline
    );
    
    return plan;
  }
}
```

### Context Persistence

```typescript
class InterviewContextManager {
  async saveContext(projectId: string, context: InterviewContext): Promise<void> {
    const contextData = {
      timestamp: new Date(),
      intent: context.detectedIntent,
      priorities: context.priorities,
      timeline: context.timeline,
      customPlan: context.customPlan,
      userGoals: this.summarizeGoals(context.userResponses)
    };
    
    await this.storage.save(
      `.kapi/interview/${projectId}/context.json`,
      contextData
    );
  }
  
  async loadContext(projectId: string): Promise<InterviewContext | null> {
    const saved = await this.storage.load(
      `.kapi/interview/${projectId}/context.json`
    );
    
    if (saved && this.isRecent(saved.timestamp)) {
      return this.reconstructContext(saved);
    }
    
    return null;
  }
}
```

## Interview Templates

### Goal Detection Patterns

```typescript
const goalPatterns = {
  demo: {
    keywords: ['demo', 'presentation', 'show', 'meeting', 'stakeholder'],
    questions: [
      "When is your demo?",
      "What features need to work for the demo?",
      "Who will be watching the demo?"
    ],
    focus: ['stability', 'ui', 'core_features']
  },
  
  deployment: {
    keywords: ['deploy', 'live', 'production', 'launch', 'ship'],
    questions: [
      "Are you deploying to production or staging?",
      "Do you have specific security requirements?",
      "What's your expected user load?"
    ],
    focus: ['security', 'performance', 'reliability']
  },
  
  maintenance: {
    keywords: ['maintain', 'team', 'handoff', 'document', 'clean'],
    questions: [
      "Will other developers work on this?",
      "What's the long-term plan for this project?",
      "Any specific coding standards to follow?"
    ],
    focus: ['code_quality', 'documentation', 'testing']
  },
  
  quick_fix: {
    keywords: ['broken', 'crash', 'error', 'bug', 'fix'],
    questions: [
      "What's the specific issue you're facing?",
      "Is this blocking other work?",
      "Have you tried any fixes already?"
    ],
    focus: ['errors', 'stability', 'functionality']
  }
};
```

### Conversation State Machine

```mermaid
stateDiagram-v2
    [*] --> Opening
    Opening --> GoalDetection
    GoalDetection --> Clarification
    Clarification --> PlanProposal
    PlanProposal --> Negotiation
    Negotiation --> PlanProposal
    PlanProposal --> Confirmation
    Confirmation --> [*]
    
    GoalDetection --> QuickPath: Urgent need detected
    QuickPath --> Confirmation
```

## Integration Points

### With Brutal Honesty Report

```typescript
// Use analysis to make conversation relevant
const opening = generateOpening(brutalHonestyReport);
// Example: "I see you have 23% readiness with major security issues. 
// What's your immediate priority for this project?"
```

### With Progress Tracking

```typescript
// Update target based on interview
progressTracker.setTarget({
  score: interviewContext.customPlan.targetReadiness,
  reason: interviewContext.userGoals.primary,
  timeline: interviewContext.timeline
});
```

### With Auto-Fix System

```typescript
// Prioritize fixes based on interview
autoFixer.setPriorities(interviewContext.priorities);
autoFixer.setTimeConstraint(interviewContext.timeline);
```

## Success Metrics

- **Goal Detection**: 85% accuracy in understanding user intent
- **Plan Acceptance**: 70% accept first recommendation
- **Completion Rate**: 90% complete interview in <5 exchanges
- **Satisfaction**: 4.5/5 rating for personalization

## Future Enhancements

1. **ML-Based Intent Detection**: Train on conversation data
2. **Multi-Goal Support**: Handle complex, competing goals
3. **Team Context**: Understand team dynamics and needs
4. **Learning System**: Improve responses based on outcomes
5. **Voice Integration**: Add voice input post-MVP

## Related Features

- [Brutal Honesty Assessment](../brutal-honesty/ai-project-analysis.md)
- [Progress Tracking](./02-progress-tracking.md)
- [Auto-Fix System](./05-auto-fix-system.md)