# Git Integration & Timeline Feature Specification

_Feature ID: MVP-GIT-001_  
_Last updated: July 15, 2025_

## Overview

Deep Git integration that shows when issues were introduced, who made changes, and provides a visual timeline of code evolution. This feature adds accountability and historical context to the brutal honesty assessment.

## Core Features

### 1. Issue Timeline Tracking

```typescript
interface IssueTimeline {
  issue: CodeIssue;
  introducedIn: {
    commit: string;
    author: string;
    date: Date;
    message: string;
  };
  lastModified: {
    commit: string;
    author: string;
    date: Date;
  };
  affectedLines: LineRange[];
  blameInfo: GitBlame[];
}
```

### 2. Visual Git Timeline

```
┌─────────────────────────────────────────┐
│  📍 Issue Timeline: Exposed API Key     │
├─────────────────────────────────────────┤
│                                         │
│  3 days ago (commit a4f2c1)            │
│  ●──────────────────────────────────●   │
│  Author: <EMAIL>                  │
│  "Added OpenAI integration"             │
│                                         │
│  ⚠️ API key hardcoded in config.js:42   │
│                                         │
│  Previous safe version:                 │
│  ○ 5 days ago (commit b7d3e2)          │
│    "Initial config setup"               │
│                                         │
│  [View Diff] [Blame View] [Notify]      │
│                                         │
└─────────────────────────────────────────┘
```

### 3. "Where You Left Off" Feature

```
┌─────────────────────────────────────────┐
│  👋 Welcome Back!                       │
├─────────────────────────────────────────┤
│                                         │
│  You last worked on this project:       │
│  2 days ago at 4:32 PM                  │
│                                         │
│  Your last commit:                      │
│  "Fixed user authentication flow"       │
│                                         │
│  Since then:                            │
│  • 3 teammates made 7 commits           │
│  • 2 new issues introduced              │
│  • 1 deployment to staging              │
│                                         │
│  Uncommitted changes:                   │
│  • src/api/users.js (modified)          │
│  • tests/auth.test.js (new file)       │
│                                         │
│  [Continue Where I Left Off] [See All]  │
│                                         │
└─────────────────────────────────────────┘
```

## Technical Implementation

### Git Analysis Service

```typescript
class GitAnalysisService {
  private git: SimpleGit;
  
  async analyzeIssueOrigin(
    filePath: string,
    lineNumber: number,
    issueType: string
  ): Promise<IssueTimeline> {
    // Get blame information
    const blame = await this.git.blame(filePath);
    const blameLine = blame.lines[lineNumber - 1];
    
    // Find when this specific issue was introduced
    const commits = await this.git.log({
      file: filePath,
      from: blameLine.hash,
      to: 'HEAD'
    });
    
    // Analyze each commit to find issue introduction
    for (const commit of commits.all) {
      const diff = await this.git.show([
        commit.hash,
        '--',
        filePath
      ]);
      
      if (this.detectIssueInDiff(diff, issueType, lineNumber)) {
        return {
          issue: { type: issueType, severity: 'high' },
          introducedIn: {
            commit: commit.hash,
            author: commit.author_email,
            date: new Date(commit.date),
            message: commit.message
          },
          lastModified: blameLine,
          affectedLines: this.getAffectedLines(diff),
          blameInfo: blame.lines
        };
      }
    }
  }
  
  async getUserActivity(projectPath: string): Promise<UserActivity> {
    const userEmail = await this.git.raw(['config', 'user.email']);
    
    // Get user's last commit
    const lastCommit = await this.git.log({
      maxCount: 1,
      author: userEmail
    });
    
    // Get uncommitted changes
    const status = await this.git.status();
    
    // Get team activity since last user commit
    const teamActivity = await this.git.log({
      since: lastCommit.latest.date,
      excludeAuthor: userEmail
    });
    
    return {
      lastCommit: lastCommit.latest,
      uncommittedChanges: status,
      teamActivity: teamActivity.all,
      lastActiveDate: new Date(lastCommit.latest.date)
    };
  }
}
```

### Blame Visualization

```typescript
class BlameVisualizer {
  renderBlameSidebar(
    fileContent: string,
    blameData: GitBlame[]
  ): BlameView {
    const lines = fileContent.split('\n');
    
    return lines.map((line, index) => {
      const blame = blameData[index];
      const age = this.getCommitAge(blame.date);
      const isIssue = this.issueLines.includes(index + 1);
      
      return {
        lineNumber: index + 1,
        content: line,
        author: blame.author,
        date: blame.date,
        commit: blame.hash.substring(0, 7),
        age: age,
        ageColor: this.getAgeColor(age),
        hasIssue: isIssue,
        issueIntroduced: isIssue ? blame.hash : null
      };
    });
  }
  
  private getAgeColor(days: number): string {
    if (days < 7) return '#4CAF50';    // Green - Recent
    if (days < 30) return '#FFC107';   // Yellow - Moderate
    if (days < 90) return '#FF9800';   // Orange - Old
    return '#F44336';                   // Red - Very old
  }
}
```

### Interactive Timeline Component

```typescript
class GitTimeline extends React.Component {
  renderTimeline(commits: Commit[], issues: IssueTimeline[]) {
    return (
      <div className="timeline">
        {commits.map((commit, index) => {
          const relatedIssues = issues.filter(
            i => i.introducedIn.commit === commit.hash
          );
          
          return (
            <div key={commit.hash} className="timeline-entry">
              <div className="timeline-marker">
                {relatedIssues.length > 0 ? '🔴' : '○'}
              </div>
              
              <div className="timeline-content">
                <div className="commit-info">
                  <span className="date">{formatDate(commit.date)}</span>
                  <span className="author">{commit.author_name}</span>
                </div>
                
                <div className="commit-message">{commit.message}</div>
                
                {relatedIssues.length > 0 && (
                  <div className="issues-introduced">
                    <strong>Issues introduced:</strong>
                    {relatedIssues.map(issue => (
                      <div key={issue.issue.id} className="issue-item">
                        {issue.issue.type}: {issue.issue.description}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  }
}
```

## Integration with Brutal Honesty Report

### Enhanced Report Display

```
┌─────────────────────────────────────────┐
│  Production Readiness: 23% 😬           │
├─────────────────────────────────────────┤
│                                         │
│  Let's be honest about your project:    │
│                                         │
│  🔴 Security: F                         │
│  "Your API keys are exposed. Anyone     │
│   could steal them in 30 seconds."      │
│  📍 Introduced: 3 days ago by john@     │
│  📄 File: config.js:42 (commit a4f2c1)  │
│  [View Timeline] [See Diff]             │
│                                         │
│  🔴 Error Handling: F                   │
│  "Your app will crash if someone        │
│   sneezes near the server."             │
│  📍 Issues across 12 files              │
│  👥 By: 3 different developers          │
│  [View All Issues] [Git Blame]          │
│                                         │
└─────────────────────────────────────────┘
```

### Commit-Aware Fixes

```typescript
class GitAwareFixer {
  async prepareFix(issue: IssueTimeline): Promise<FixContext> {
    // Check if file has been modified since issue introduction
    const currentBlame = await this.git.blame(issue.filePath);
    const hasChanged = currentBlame[issue.line].hash !== issue.introducedIn.commit;
    
    if (hasChanged) {
      // Need to handle merge conflicts
      return {
        strategy: 'careful',
        warning: 'File has been modified since issue was introduced',
        suggestedApproach: this.getMergeStrategy(issue)
      };
    }
    
    return {
      strategy: 'direct',
      canAutoFix: true
    };
  }
  
  async applyFixWithGit(fix: Fix): Promise<void> {
    // Create a meaningful commit message
    const message = `fix: ${fix.description}\n\n` +
                   `Fixes ${fix.issueType} introduced in ${fix.originalCommit}\n` +
                   `Improves readiness score by ${fix.pointsEarned}%`;
    
    // Apply the fix
    await this.fileService.writeFile(fix.filePath, fix.newContent);
    
    // Stage and commit
    await this.git.add(fix.filePath);
    await this.git.commit(message);
  }
}
```

## Git Statistics Dashboard

```
┌─────────────────────────────────────────┐
│  📊 Project Git Health                  │
├─────────────────────────────────────────┤
│                                         │
│  Code Age Distribution:                 │
│  < 1 week:  ████████ 35%               │
│  < 1 month: ██████ 25%                 │
│  < 3 months:████ 20%                   │
│  > 3 months:████ 20%                   │
│                                         │
│  Most Active Contributors:              │
│  1. <EMAIL>    (145 commits)     │
│  2. <EMAIL>      (89 commits)      │
│  3. <EMAIL>      (67 commits)      │
│                                         │
│  Issue Introduction Rate:               │
│  This week: 3 issues (↓ from 7)         │
│  This month: 12 issues total            │
│                                         │
│  [Detailed History] [Team Stats]        │
│                                         │
└─────────────────────────────────────────┘
```

## Storage Integration

### Git Cache Structure

```
.kapi/
└── git-cache/
    ├── blame-cache.json     # Cached blame data
    ├── issue-timeline.json  # Issue introduction history
    ├── user-activity.json   # User's last activity
    └── team-stats.json      # Team contribution stats
```

### Cache Management

```typescript
class GitCacheManager {
  private cacheDir = '.kapi/git-cache';
  private maxAge = 3600000; // 1 hour
  
  async getBlameData(filePath: string): Promise<GitBlame[]> {
    const cacheKey = this.getCacheKey(filePath);
    const cached = await this.readCache(cacheKey);
    
    if (cached && !this.isExpired(cached)) {
      return cached.data;
    }
    
    // Fetch fresh data
    const fresh = await this.git.blame(filePath);
    await this.writeCache(cacheKey, fresh);
    
    return fresh;
  }
}
```

## Performance Considerations

### Optimization Strategies

1. **Incremental Blame**: Only blame files with detected issues
2. **Commit Range Limiting**: Analyze last 90 days by default
3. **Parallel Processing**: Blame multiple files concurrently
4. **Smart Caching**: Cache blame data with TTL
5. **Shallow Clone Support**: Work with partial history

```typescript
class PerformantGitAnalyzer {
  async analyzeProjectEfficiently(files: string[]): Promise<Analysis> {
    // Filter to only files with issues
    const problematicFiles = files.filter(f => this.hasIssues(f));
    
    // Parallel blame with concurrency limit
    const blamePromises = problematicFiles.map(
      file => this.limiter.schedule(() => this.git.blame(file))
    );
    
    const blameResults = await Promise.all(blamePromises);
    
    // Cache results
    await this.cacheManager.storeBlameData(blameResults);
    
    return this.processBlameData(blameResults);
  }
}
```

## Success Metrics

- **Issue Attribution**: 95% of issues linked to commits
- **Performance**: <3s for blame analysis on 100 files
- **Cache Hit Rate**: >80% for repeat analyses
- **User Engagement**: 70% click "View Timeline"

## Future Enhancements

1. **PR Integration**: Link issues to pull requests
2. **Team Notifications**: Alert when issues introduced
3. **Git Hooks**: Pre-commit issue detection
4. **Branch Analysis**: Compare branch health
5. **Historical Trends**: Issue introduction over time

## Related Features

- [Brutal Honesty Assessment](../brutal-honesty/ai-project-analysis.md)
- [Progress Tracking](./02-progress-tracking.md)
- [Auto-Fix System](./05-auto-fix-system.md)